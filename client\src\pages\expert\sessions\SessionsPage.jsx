import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import {
  VideoCameraIcon,
  ClockIcon,
  CalendarIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  BellIcon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  StarIcon,
  PlayCircleIcon,
  PaperClipIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Link } from 'react-router-dom';
import api from '../../../services/api';
import toast from 'react-hot-toast';

/**
 * <PERSON>zman görüşmeleri sayfası
 */
const SessionsPage = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [sessions, setSessions] = useState([]);
  const [activeTab, setActiveTab] = useState('upcoming'); // upcoming, past, all
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isCreatingMeeting, setIsCreatingMeeting] = useState(false);

  // Mock görüşme durumları
  const sessionStatuses = {
    scheduled: "Planlandı",
    inProgress: "Devam Ediyor",
    completed: "Tamamlandı",
    missed: "Kaçırıldı",
    cancelled: "İptal Edildi",
  };

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setIsLoading(true);
      // Expert appointments'ları al (confirmed olanlar sessions olarak gösterilecek)
      const response = await api.get('/experts/appointments');
      const appointments = response.data.appointments || [];

      // Sadece confirmed appointments'ları sessions olarak göster
      const confirmedSessions = appointments
        .filter(apt => apt.Status === 'Confirmed')
        .map(apt => ({
          id: apt.AppointmentID,
          clientId: apt.ClientID,
          clientName: `${apt.ClientFirstName} ${apt.ClientLastName}`,
          clientEmail: apt.ClientEmail,
          clientAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(apt.ClientFirstName)}+${encodeURIComponent(apt.ClientLastName)}&background=random&size=40`,
          appointmentDate: apt.AppointmentDate,
          endTime: apt.EndTime,
          status: apt.Status,
          notes: apt.Notes,
          meetingLink: apt.MeetingLink,
          sessionStatus: getSessionStatus(apt),
          canJoinMeeting: canJoinMeeting(apt.AppointmentDate),
          isUpcoming: new Date(apt.AppointmentDate) > new Date(),
          isPast: new Date(apt.AppointmentDate) < new Date()
        }));

      console.log('🔍 Confirmed Sessions:', confirmedSessions.length);
      confirmedSessions.forEach((session, index) => {
        console.log(`  ${index + 1}. Session:`, {
          id: session.id,
          meetingLink: session.meetingLink,
          canJoinMeeting: session.canJoinMeeting,
          sessionStatus: session.sessionStatus,
          appointmentDate: session.appointmentDate
        });
      });

      setSessions(confirmedSessions);
    } catch (error) {
      console.error('Sessions yükleme hatası:', error);
      toast.error('Seanslar yüklenemedi');
    } finally {
      setIsLoading(false);
    }
  };

  // Seans durumunu belirle
  const getSessionStatus = (appointment) => {
    const now = new Date();
    const appointmentTime = new Date(appointment.AppointmentDate);
    const endTime = new Date(appointment.EndTime);

    if (now < appointmentTime) {
      return 'scheduled'; // Planlandı
    } else if (now >= appointmentTime && now <= endTime) {
      return 'inProgress'; // Devam ediyor
    } else {
      return 'completed'; // Tamamlandı
    }
  };

  // Meeting'e katılabilir mi kontrol et (TEST MODU)
  const canJoinMeeting = (appointmentDate) => {
    // TEST MODU: Onaylandığı andan itibaren katılabilir
    return true;

    /* PRODUCTION MODU (test sonrası aktifleştir):
    const now = new Date();
    const appointmentTime = new Date(appointmentDate);
    const timeDiff = appointmentTime.getTime() - now.getTime();

    // 15 dakika öncesinden 1 saat sonrasına kadar katılabilir
    return timeDiff <= 15 * 60 * 1000 && timeDiff >= -60 * 60 * 1000;
    */
  };

  // Meeting link kontrolü - P2P WebRTC kullanıyoruz

  // İstatistik hesaplamaları
  const stats = {
    total: sessions.length,
    upcoming: sessions.filter(s => s.sessionStatus === 'scheduled').length,
    completed: sessions.filter(s => s.sessionStatus === 'completed').length,
    missed: sessions.filter(s => s.sessionStatus === 'missed').length,
    cancelled: sessions.filter(s => s.sessionStatus === 'cancelled').length
  };

  // Debug: İstatistikleri console'da göster
  console.log('📊 Sessions Stats:', {
    total: stats.total,
    upcoming: stats.upcoming,
    completed: stats.completed
  });

  // Bugünün tarihi
  const today = new Date();
  
  // Görüşmeleri filtrele
  const filteredSessions = sessions.filter(session => {
    const sessionDate = parseISO(session.appointmentDate);

    // Tab filtresi
    if (activeTab === 'upcoming') {
      if (!(sessionDate >= today && session.sessionStatus === 'scheduled')) {
        return false;
      }
    } else if (activeTab === 'past') {
      if (!(sessionDate < today || session.sessionStatus === 'completed' || session.sessionStatus === 'missed' || session.sessionStatus === 'cancelled')) {
        return false;
      }
    }
    // activeTab === 'all' için herhangi bir filtreleme yapmıyoruz

    // Durum filtresi
    if (filterStatus !== 'all' && session.sessionStatus !== filterStatus) {
      return false;
    }

    // Arama filtresi
    if (searchTerm && !session.clientName.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    return true;
  });

  // Tarihe göre sırala
  const sortedSessions = [...filteredSessions].sort((a, b) => {
    // Önce tarihleri karşılaştır
    const dateComparison = new Date(b.appointmentDate) - new Date(a.appointmentDate);
    if (dateComparison !== 0) return dateComparison;

    // Tarihler aynıysa başlama saatini karşılaştır
    return new Date(a.appointmentDate) - new Date(b.appointmentDate);
  });

  // Durum badge renkleri
  const getStatusBadge = (status) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'missed':
        return 'bg-amber-100 text-amber-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Yükleniyor durumu
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-12">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 pt-4 sm:pt-8">
        {/* Başlık ve Üst Kısım */}
        <div className="bg-gradient-to-r from-orange-500 to-orange-700 shadow-lg rounded-lg p-4 sm:p-6 mb-4 sm:mb-6 text-white">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold">Terapist Seanslarım</h1>
              <p className="mt-1 text-orange-100 text-sm sm:text-base">
                Gerçekleşecek ve gerçekleşmiş tüm terapist seanslarınızı yönetin
              </p>
            </div>
            <div className="mt-3 sm:mt-0 flex space-x-2">
              <Link
                to="/expert/appointments"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-purple-800 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-300"
              >
                <CalendarIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                Randevularım
              </Link>
              <button className="inline-flex items-center px-3 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white bg-purple-700 bg-opacity-50 hover:bg-opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                <ArrowDownTrayIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                Rapor İndir
              </button>
              <button className="relative p-1 rounded-full bg-purple-700 bg-opacity-50 text-purple-100 hover:text-white focus:outline-none">
                <BellIcon className="h-6 w-6" />
                <span className="absolute top-0 right-0 block h-2.5 w-2.5 rounded-full bg-red-400 ring-2 ring-purple-700"></span>
              </button>
            </div>
          </div>
        </div>

        {/* Özet İstatistikler */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-purple-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'all' ? 'ring-2 ring-purple-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('all');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Toplam</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.total}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-blue-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'upcoming' ? 'ring-2 ring-blue-500' : ''}`}
            onClick={() => {
              setActiveTab('upcoming');
              setFilterStatus('scheduled');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Planlanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.upcoming}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-green-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'completed' ? 'ring-2 ring-green-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('completed');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Tamamlanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.completed}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-amber-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'missed' ? 'ring-2 ring-amber-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('missed');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Kaçırılan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.missed}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-red-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'cancelled' ? 'ring-2 ring-red-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('cancelled');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">İptal Edilen</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.cancelled}</span>
          </div>
        </div>

        {/* Ana Sekme Navigasyonu */}
        <div className="overflow-x-auto scrollbar-hide border-b border-gray-200 mb-6">
          <div className="flex min-w-max">
            <button
              onClick={() => {
                setActiveTab('upcoming');
                setFilterStatus('scheduled');
              }}
              className={`py-4 px-4 sm:px-6 text-center border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'upcoming'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <CalendarIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" />
                <span className="text-xs sm:text-sm">Yaklaşan</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('past')}
              className={`py-4 px-4 sm:px-6 text-center border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'past'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <CheckCircleIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" />
                <span className="text-xs sm:text-sm">Geçmiş</span>
              </div>
            </button>
            <button
              onClick={() => {
                setActiveTab('all');
                setFilterStatus('all');
              }}
              className={`py-4 px-4 sm:px-6 text-center border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'all'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <DocumentTextIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" />
                <span className="text-xs sm:text-sm">Tümü</span>
              </div>
            </button>
          </div>
        </div>

        {/* Arama */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="max-w-lg">
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                  placeholder="Danışan adına göre ara..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Görüşmeler Listesi */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              {activeTab === 'upcoming' ? 'Yaklaşan Seanslar' :
               activeTab === 'past' ? 'Geçmiş Seanslar' : 'Tüm Seanslar'}
              {filterStatus !== 'all' && ` - ${sessionStatuses[filterStatus]}`}
            </h2>
          </div>

          {sortedSessions.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {sortedSessions.map((session) => (
                <div key={session.id} className="p-4 sm:p-6 hover:bg-gray-50 transition duration-150">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div className="flex items-center space-x-3 min-w-0 flex-1">
                      <div className="flex-shrink-0">
                        <img
                          className="h-8 w-8 sm:h-10 sm:w-10 rounded-full border border-gray-200"
                          src={session.clientAvatar}
                          alt={session.clientName}
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <h3 className="text-sm font-medium text-gray-900 truncate">{session.clientName}</h3>
                        <div className="flex flex-wrap gap-1 sm:gap-2 text-xs text-gray-500">
                          <span>{format(parseISO(session.appointmentDate), 'EEEE', { locale: tr })}</span>
                          <span>•</span>
                          <span>{format(parseISO(session.appointmentDate), 'd MMMM yyyy', { locale: tr })}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-end sm:justify-start">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(session.sessionStatus)}`}>
                        {sessionStatuses[session.sessionStatus]}
                      </span>
                    </div>
                  </div>

                  <div className="mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div className="flex flex-col sm:flex-row sm:space-x-6 space-y-2 sm:space-y-0 text-sm text-gray-500">
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>
                          {format(parseISO(session.appointmentDate), 'HH:mm')} - {format(parseISO(session.endTime), 'HH:mm')}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <UserIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>Randevu #{session.id}</span>
                      </div>
                      {session.notes && (
                        <div className="flex items-center">
                          <DocumentTextIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                          <span className="truncate max-w-32">{session.notes}</span>
                        </div>
                      )}
                    </div>

                    {/* Mobilde kaydırılabilir buton konteyner */}
                    <div className="overflow-x-auto scrollbar-hide">
                      <div className="flex space-x-2 min-w-max pb-1">
                        {/* P2P Meeting Katılma Butonu */}
                        {session.meetingLink && session.canJoinMeeting && (
                          <button
                            type="button"
                            onClick={() => {
                              console.log('🚀 P2P Meeting sayfasına yönlendiriliyor, randevu:', session.id);
                              window.open(`/meeting/${session.id}`, '_blank');
                            }}
                            className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 whitespace-nowrap"
                          >
                            <VideoCameraIcon className="-ml-0.5 mr-1 h-4 w-4" />
                            Görüşmeye Katıl
                          </button>
                        )}

                        {/* Meeting Link Hazır Ama Henüz Katılamaz */}
                        {session.meetingLink && !session.canJoinMeeting && session.sessionStatus === 'scheduled' && (
                          <div className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-500 bg-gray-50 whitespace-nowrap">
                            <VideoCameraIcon className="-ml-0.5 mr-1 h-4 w-4" />
                            Görüşme Hazır
                          </div>
                        )}

                        {/* Meeting Link Yoksa Bilgi Mesajı */}
                        {!session.meetingLink && session.sessionStatus === 'scheduled' && (
                          <div className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-500 bg-gray-50 whitespace-nowrap">
                            <VideoCameraIcon className="-ml-0.5 mr-1 h-4 w-4" />
                            Görüşme Odası Hazırlanıyor
                          </div>
                        )}

                        {/* Mesaj Gönder */}
                        <Link
                          to={`/expert/messages?clientId=${session.clientId}`}
                          className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 whitespace-nowrap"
                        >
                          <ChatBubbleLeftRightIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Mesaj
                        </Link>

                        {/* Notlar */}
                        <button
                          type="button"
                          className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 whitespace-nowrap"
                        >
                          <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Seans Notları
                        </button>

                        <button
                          type="button"
                          className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 whitespace-nowrap"
                        >
                          <ChatBubbleLeftRightIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Danışana Mesaj
                        </button>
                      </div>
                    </div>
                  </div>

                  {session.notes && (
                    <div className="mt-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-md">
                      <span className="font-medium">Not:</span> {session.notes}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="py-12 text-center">
              <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Seans Bulunamadı</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || filterStatus !== 'all'
                  ? 'Arama kriterlerinize uygun seans bulunamadı.'
                  : 'Henüz bir seansınız bulunmuyor.'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SessionsPage; 
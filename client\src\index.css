@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply h-full bg-gray-50;
  }
  body {
    @apply h-full font-sans text-gray-900 antialiased;
  }
  #root {
    @apply h-full;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f3f4f6;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Form styles */
  label {
    @apply block text-sm font-medium text-gray-700;
  }
  
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="date"],
  input[type="search"],
  select,
  textarea {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md border border-transparent px-4 py-2 text-sm font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-white {
    @apply border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .card {
    @apply overflow-hidden rounded-lg bg-white shadow;
  }
  
  .card-header {
    @apply border-b border-gray-200 bg-gray-50 px-4 py-5 sm:px-6;
  }
  
  .card-body {
    @apply px-4 py-5 sm:p-6;
  }
  
  .card-footer {
    @apply border-t border-gray-200 bg-gray-50 px-4 py-4 sm:px-6;
  }

  /* Scrollbar hide utility */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { 
  CalendarIcon, 
  PlusIcon, 
  TrashIcon, 
  PencilIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
  CheckIcon,
  XMarkIcon,
  ClockIcon,
  ArrowPathIcon,
  InformationCircleIcon,
  BellIcon,
  ArrowDownTrayIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { format, addWeeks, subWeeks, startOfWeek, addDays, isSameDay, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import { expertAvailabilityApi } from '../../../services/api';
import toast from 'react-hot-toast';

/**
 * Uzman müsaitlik takvimi ve düzenleme sayfası
 */
const AvailabilityPage = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  
  // <PERSON>ug<PERSON><PERSON><PERSON>n tarihini al
  const todayDate = new Date();
  const [currentDate, setCurrentDate] = useState(todayDate);
  
  const [availabilities, setAvailabilities] = useState([]);
  const [selectedDay, setSelectedDay] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editingTimeSlot, setEditingTimeSlot] = useState(null);
  const [startTime, setStartTime] = useState('09:00');
  const [endTime, setEndTime] = useState('17:00');
  const [isRecurring, setIsRecurring] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Zaman dilimi seçenekleri
  const timeOptions = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
    '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00'
  ];

  useEffect(() => {
    fetchAvailabilities();
  }, []);

  // API'den müsaitlik verilerini çek
  const fetchAvailabilities = async () => {
    setIsLoading(true);
    try {
      const response = await expertAvailabilityApi.getAvailability();

      // API'den gelen verileri formatla - yeni format: {availability: [...]}
      const availabilityData = response.data.availability || response.data;
      const formattedData = formatAvailabilityData(availabilityData);
      setAvailabilities(formattedData);
    } catch (error) {
      console.error('Müsaitlik bilgileri alınamadı:', error);
      toast.error('Müsaitlik bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  // API'den gelen verileri UI için formatla
  const formatAvailabilityData = (apiData) => {
    // Zaman formatını düzelt
    const formatTime = (timeString) => {
      if (!timeString) return '';
      
      // "HH:MM:SS" formatını koru, fazladan gelen ISO tarih bilgisini temizle
      if (timeString.includes('T')) {
        return timeString.split('T')[1].substring(0, 5); // "09:00:00" -> "09:00"
      }
      
      // "HH:MM:SS" formatını "HH:MM" formatına dönüştür
      return timeString.substring(0, 5); // "09:00:00" -> "09:00"
    };

    // Her gün için müsaitlik saatlerini grupla
    const groupedByDay = apiData.reduce((acc, item) => {
      const day = item.dayOfWeek;
      
      if (!acc[day]) {
        acc[day] = {
          id: day,
          day: day,
          hours: []
        };
      }
      
      acc[day].hours.push({
        id: item.id,
        start: formatTime(item.startTime),
        end: formatTime(item.endTime),
        isRecurring: item.isRecurring,
        specificDate: item.specificDate
      });
      
      return acc;
    }, {});
    
    // Object.values ile objeyi diziye çevir
    return Object.values(groupedByDay);
  };

  // Hafta görünümü için günler
  const getDaysForWeekView = () => {
    // "Bugün" (25 Mart 2025, Salı) gününü haftanın 2. günü olarak kabul edip
    // Pazartesi gününden (24 Mart) başlayarak 7 günlük bir liste oluştur
    
    // Bugünün haftanın kaçıncı günü olduğunu bul (0: Pazar, 1: Pazartesi, ...)
    const currentDayOfWeek = currentDate.getDay();
    
    // Pazartesi gününe gitmek için kaç gün geriye gitmemiz gerektiğini hesapla
    // Eğer bugün Pazar (0) ise, 6 gün geriye git. Diğer günler için ise (gün - 1) gün geriye git
    const daysToSubtract = currentDayOfWeek === 0 ? 6 : currentDayOfWeek - 1;
    
    // Pazartesi gününü bul
    const mondayDate = new Date(currentDate);
    mondayDate.setDate(currentDate.getDate() - daysToSubtract);
    
    // Pazartesi'den başlayarak haftanın 7 gününü oluştur
    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(mondayDate);
      day.setDate(mondayDate.getDate() + i);
      days.push(day);
    }
    
    return days;
  };

  // Sonraki haftaya geç
  const nextWeek = () => {
    setCurrentDate(addWeeks(currentDate, 1));
  };

  // Önceki haftaya geç - bugünden öncesine gidilmesini engelle
  const prevWeek = () => {
    const newDate = subWeeks(currentDate, 1);
    const startOfNewWeek = startOfWeek(newDate, { weekStartsOn: 1 }); // Pazartesi başlangıç
    const startOfCurrentWeek = startOfWeek(new Date(), { weekStartsOn: 1 });

    // Yeni haftanın başlangıcı bugünün haftasından önceyse, bugünün haftasına git
    if (startOfNewWeek < startOfCurrentWeek) {
      setCurrentDate(new Date());
    } else {
      setCurrentDate(newDate);
    }
  };

  // Bugüne dön
  const goToToday = () => {
    setCurrentDate(new Date());
  };

  // Gün için müsait saatleri getir
  const getAvailabilityForDay = (date) => {
    // JavaScript'in getDay() metodu 0-6 döndürür (0: Pazar, 1: Pazartesi)
    // Backend'den gelen değerler de bu formatta, direkt olarak kullanabiliriz
    const dayOfWeek = date.getDay(); // 0-6 formatında (backend ile uyumlu)
    
    // Haftanın günü için kaydı bul
    const dayData = availabilities.find(a => a.day === dayOfWeek) || { day: dayOfWeek, hours: [] };
    
    // Saatleri filtrele - tekrarlanan veya bu tarihe özel olanları getir
    const filteredHours = dayData.hours.filter(hour => {
      // Tekrarlanan saatler her zaman gösterilir
      if (hour.isRecurring) return true;

      // Tekrarlanmayan (tek seferlik) saatler sadece tam o tarihte gösterilir
      if (!hour.isRecurring && hour.specificDate) {
        const specificDate = parseISO(hour.specificDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Bugünün başlangıcı

        // Geçmiş tarihlerdeki tek seferlik müsaitlikleri gösterme
        if (specificDate < today) return false;

        return isSameDay(specificDate, date);
      }

      return false;
    });
    
    // Yeni filtrelenmiş saatlerle günü döndür
    return {
      ...dayData,
      hours: filteredHours
    };
  };

  // Gün seç
  const handleDaySelect = (day) => {
    const dayData = getAvailabilityForDay(day);
    setSelectedDay({ date: day, ...dayData });
  };

  // Gün formatını oluştur
  const formatDayHeader = (date) => {
    return (
      <>
        <p className="text-sm font-medium">
          {format(date, 'EEEE', { locale: tr })}
        </p>
        <p className="text-sm text-gray-500">
          {format(date, 'd MMMM', { locale: tr })}
        </p>
      </>
    );
  };

  // Saat dilimini düzenlemeye başla
  const handleEditTimeSlot = (timeSlot) => {
    setEditingTimeSlot(timeSlot);
    setStartTime(timeSlot.start);
    setEndTime(timeSlot.end);
    setIsRecurring(timeSlot.isRecurring);
    setIsEditing(true);
  };

  // Yeni saat dilimi eklemeye başla
  const handleAddTimeSlot = () => {
    setEditingTimeSlot(null);
    setStartTime('09:00');
    setEndTime('17:00');
    setIsRecurring(false);
    setIsEditing(true);
  };

  // Saat dilimini sil
  const handleDeleteTimeSlot = async (timeSlotId) => {
    if (window.confirm('Bu zaman dilimini silmek istediğinize emin misiniz?')) {
      if (selectedDay) {
        try {
          setIsSaving(true);
          await expertAvailabilityApi.deleteAvailability(timeSlotId);
          
          const updatedHours = selectedDay.hours.filter(h => h.id !== timeSlotId);
          const updatedDay = { ...selectedDay, hours: updatedHours };
          setSelectedDay(updatedDay);
          
          // Tüm availability listesini güncelle - gün kodu 0-6 formatındadır (backend ile uyumlu)
          const updatedAvailabilities = availabilities.map(a => 
            a.day === selectedDay.day ? { ...a, hours: updatedHours } : a
          );
          setAvailabilities(updatedAvailabilities);
          
          toast.success('Çalışma saati başarıyla silindi', {
            icon: '🗑️',
            style: {
              borderRadius: '10px',
              background: '#FFEDED',
              color: '#B91C1C',
              border: '1px solid #FCA5A5',
            },
          });
        } catch (error) {
          console.error('Çalışma saati silinemedi:', error);
          toast.error('Çalışma saati silinirken bir hata oluştu');
        } finally {
          setIsSaving(false);
        }
      }
    }
  };

  // Düzenlemeyi kaydet
  const handleSaveTimeSlot = async () => {
    if (!startTime || !endTime || startTime >= endTime) {
      toast.error('Lütfen geçerli bir zaman aralığı seçin.');
      return;
    }

    if (selectedDay) {
      // Backend'e gönderilecek gün değeri için JavaScript'in getDay() metodunu kullan (0: Pazar, 1: Pazartesi, ..., 6: Cumartesi)
      // Backend 0-6 formatını bekliyor, frontend ise 1-7 formatını kullanıyor
      const dayOfWeek = selectedDay.date.getDay();
      
      try {
        setIsSaving(true);
        let availabilityId;
        
        // SQL Server TIME veri tipi için doğru format: HH:MM:SS
        // Eğer sadece HH:MM formatı varsa, saniye ekleyerek düzelt
        const formatTimeForSQL = (timeString) => {
          // HH:MM formatını kontrol et
          if (/^\d{2}:\d{2}$/.test(timeString)) {
            return `${timeString}:00`;
          }
          return timeString;
        };
        
        // Tekrarlanan müsaitlik için specificDate: null
        // Tekrarlanmayan müsaitlik için o günün tarihi
        const specificDateValue = isRecurring ? null : format(selectedDay.date, 'yyyy-MM-dd');
        
        const availabilityData = {
          dayOfWeek,
          startTime: formatTimeForSQL(startTime),
          endTime: formatTimeForSQL(endTime),
          isRecurring,
          specificDate: specificDateValue
        };
        
        console.log('Gönderilecek veri:', availabilityData);
        
        if (editingTimeSlot) {
          // Mevcut zaman dilimini güncelle
          await expertAvailabilityApi.updateAvailability(editingTimeSlot.id, availabilityData);
          availabilityId = editingTimeSlot.id;
          toast.success('Çalışma saati başarıyla güncellendi', {
            icon: '✏️',
            style: {
              borderRadius: '10px',
              background: '#ECFDF5',
              color: '#047857',
              border: '1px solid #6EE7B7',
            },
          });
        } else {
          // Yeni zaman dilimi ekle
          const response = await expertAvailabilityApi.addAvailability(availabilityData);
          availabilityId = response.data.id;
          toast.success('Yeni çalışma saati başarıyla eklendi', {
            icon: '✅',
            style: {
              borderRadius: '10px',
              background: '#ECFDF5',
              color: '#047857',
              border: '1px solid #6EE7B7',
            },
          });
        }
        
        // Yerel state güncelleme
        let updatedHours;
        
        if (editingTimeSlot) {
          updatedHours = selectedDay.hours.map(h => 
            h.id === editingTimeSlot.id ? { ...h, start: startTime, end: endTime } : h
          );
        } else {
          updatedHours = [...selectedDay.hours, { id: availabilityId, start: startTime, end: endTime }];
        }
        
        // Saatleri sıralama
        updatedHours.sort((a, b) => a.start.localeCompare(b.start));
        
        const updatedDay = { ...selectedDay, hours: updatedHours };
        setSelectedDay(updatedDay);
        
        // Tüm availability listesini güncelle
        let updatedAvailabilities;
        const existingDay = availabilities.find(a => a.day === selectedDay.day);
        
        if (existingDay) {
          updatedAvailabilities = availabilities.map(a => 
            a.day === selectedDay.day ? { ...a, hours: updatedHours } : a
          );
        } else {
          updatedAvailabilities = [...availabilities, { 
            id: dayOfWeek, 
            day: dayOfWeek, 
            hours: updatedHours 
          }];
        }
        
        setAvailabilities(updatedAvailabilities);
        setIsEditing(false);
        setHasChanges(true);
      } catch (error) {
        console.error('Çalışma saati kaydedilemedi:', error);
        toast.error('Çalışma saati kaydedilirken bir hata oluştu');
      } finally {
        setIsSaving(false);
      }
    }
  };

  // Düzenlemeyi iptal et
  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  // Gün adını formatla
  const formatDayName = (day) => {
    const dayNames = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];
    return dayNames[day === 7 ? 0 : day];
  };

  const days = getDaysForWeekView();

  // Yükleniyor durumu
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-12">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 pt-4 sm:pt-8">
        {/* Başlık ve Üst Kısım */}
        <div className="bg-gradient-to-r from-violet-500 to-violet-700 shadow-lg rounded-lg p-4 sm:p-6 mb-4 sm:mb-6 text-white">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <div>
              <h1 className="text-xl sm:text-2xl font-bold">Müsaitlik Takvimi</h1>
              <p className="mt-1 text-violet-100 text-sm sm:text-base">
              Danışanların randevu alabileceği zaman dilimlerini yönetin
            </p>
          </div>
            <div className="mt-3 sm:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
            <button
                onClick={() => {
                  if (!selectedDay) {
                    const today = new Date();
                    handleDaySelect(today);
                  }
                  handleAddTimeSlot();
                }}
                className="inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-violet-800 bg-white hover:bg-violet-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-300"
              >
                <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                Yeni Müsaitlik
            </button>
              <div className="flex space-x-2">
                <button className="inline-flex items-center justify-center px-3 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white bg-violet-700 bg-opacity-50 hover:bg-opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                  <Cog6ToothIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                  <span className="hidden sm:inline">Ayarlar</span>
                </button>
                <button className="relative p-2 rounded-full bg-violet-700 bg-opacity-50 text-violet-100 hover:text-white focus:outline-none">
                  <BellIcon className="h-5 w-5 sm:h-6 sm:w-6" />
                  <span className="absolute top-0 right-0 block h-2 w-2 sm:h-2.5 sm:w-2.5 rounded-full bg-red-400 ring-2 ring-violet-700"></span>
                </button>
              </div>
          </div>
        </div>
      </div>

        {/* Bilgi Kutusu */}
        <div className="bg-white shadow-md rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <InformationCircleIcon className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3 text-sm text-gray-600">
                <p className="text-xs sm:text-sm">Aşağıdaki takvimden müsait olduğunuz günleri seçin ve her gün için çalışma saatlerinizi belirleyin.
                Danışanlar bu zaman dilimlerinde randevu alabilirler.</p>
                <div className="mt-2 space-y-1.5 text-xs">
                  <div className="flex flex-col sm:flex-row sm:items-center">
                    <span className="inline-flex items-center mr-2 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 mb-1 sm:mb-0">
                      Her hafta
                    </span>
                    <span>Her hafta aynı gün ve saatte tekrarlanan müsaitlik.</span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center">
                    <span className="inline-flex items-center mr-2 rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 mb-1 sm:mb-0">
                      Tek seferlik
                    </span>
                    <span>Sadece belirtilen tarihte geçerli olan müsaitlik.</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

        {/* Takvim Kontrolleri */}
        <div className="bg-white shadow-md rounded-lg p-3 sm:p-4 mb-4 sm:mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          <h2 className="text-base sm:text-lg font-medium text-gray-900 text-center sm:text-left">
            {format(days[0], 'd MMMM', { locale: tr })} - {format(days[6], 'd MMMM yyyy', { locale: tr })}
          </h2>
          <div className="flex justify-center sm:justify-end space-x-2">
            <button
              onClick={goToToday}
              className="inline-flex items-center px-2 sm:px-3 py-1.5 border border-gray-300 text-xs sm:text-sm leading-5 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2"
            >
              <ArrowPathIcon className="mr-1 sm:mr-1.5 h-3 w-3 sm:h-4 sm:w-4" />
              Bugün
            </button>
            <button
              onClick={prevWeek}
              className="inline-flex items-center px-2 py-1.5 border border-gray-300 text-sm leading-5 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2"
            >
              <ChevronLeftIcon className="h-4 w-4" />
            </button>
            <button
              onClick={nextWeek}
              className="inline-flex items-center px-2 py-1.5 border border-gray-300 text-sm leading-5 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2"
            >
              <ChevronRightIcon className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Haftalık Takvim Görünümü */}
        <div className="bg-white shadow-md rounded-lg p-3 sm:p-6 mb-4 sm:mb-6">
          {/* Mobil için kaydırılabilir görünüm */}
          <div className="block sm:hidden">
            <div className="overflow-x-auto scrollbar-hide">
              <div className="flex space-x-3 pb-2" style={{ minWidth: 'max-content' }}>
                {days.map((day, index) => {
                  const dayData = getAvailabilityForDay(day);
                  const isSelected = selectedDay && isSameDay(selectedDay.date, day);
                  const validHours = dayData.hours;
                  const recurringHours = validHours.filter(h => h.isRecurring);
                  const oneTimeHours = validHours.filter(h => !h.isRecurring);

                  return (
                    <div
                      key={index}
                      onClick={() => handleDaySelect(day)}
                      className={`
                        flex-shrink-0 w-32 border-2 rounded-xl p-4 cursor-pointer transition-all duration-200 shadow-sm
                        ${isSelected
                          ? 'border-violet-500 bg-violet-50 shadow-md transform scale-105'
                          : 'border-gray-200 hover:border-violet-300 hover:bg-gray-50 hover:shadow-md'
                        }
                        ${validHours.length > 0 ? 'ring-2 ring-violet-100' : ''}
                      `}
                    >
                      <div className="text-center">
                        <p className="text-sm font-bold text-gray-900">
                          {format(day, 'EEE', { locale: tr })}
                        </p>
                        <p className="text-lg font-semibold text-violet-600 mt-1">
                          {format(day, 'd', { locale: tr })}
                        </p>
                        <p className="text-xs text-gray-500">
                          {format(day, 'MMM', { locale: tr })}
                        </p>
                      </div>
                      <div className="mt-3">
                        {validHours.length > 0 ? (
                          <div className="space-y-1">
                            <div className="flex items-center justify-center">
                              <ClockIcon className="h-3 w-3 text-violet-500 mr-1" />
                              <span className="text-xs font-medium text-violet-700">
                                {validHours.length} saat
                              </span>
                            </div>
                            {validHours.slice(0, 2).map((timeSlot, i) => (
                              <div key={i} className="text-center">
                                <span className="text-xs text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full">
                                  {timeSlot.start.slice(0, 5)}-{timeSlot.end.slice(0, 5)}
                                </span>
                              </div>
                            ))}
                            {validHours.length > 2 && (
                              <div className="text-xs text-center text-gray-500">
                                +{validHours.length - 2} daha
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center">
                            <div className="w-8 h-8 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-1">
                              <span className="text-gray-400 text-xs">×</span>
                            </div>
                            <span className="text-xs text-gray-400">Müsait değil</span>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Desktop için grid görünüm */}
          <div className="hidden sm:block">
            <div className="grid grid-cols-7 gap-2">
              {days.map((day, index) => {
                const dayData = getAvailabilityForDay(day);
                const isSelected = selectedDay && isSameDay(selectedDay.date, day);
                const validHours = dayData.hours;
                const recurringHours = validHours.filter(h => h.isRecurring);
                const oneTimeHours = validHours.filter(h => !h.isRecurring);

                return (
                  <div
                    key={index}
                    onClick={() => handleDaySelect(day)}
                    className={`
                      border-2 rounded-lg px-3 py-4 cursor-pointer transition-all duration-200 min-h-[120px]
                      ${isSelected
                        ? 'border-violet-500 bg-violet-50 shadow-md'
                        : 'border-gray-200 hover:border-violet-300 hover:bg-gray-50 hover:shadow-sm'
                      }
                      ${validHours.length > 0 ? 'ring-1 ring-violet-100' : ''}
                    `}
                  >
                    <div className="text-center">
                      <p className="text-sm font-semibold text-gray-900">
                        {format(day, 'EEE', { locale: tr })}
                      </p>
                      <p className="text-lg font-bold text-violet-600 mt-1">
                        {format(day, 'd', { locale: tr })}
                      </p>
                      <p className="text-xs text-gray-500">
                        {format(day, 'MMM', { locale: tr })}
                      </p>
                    </div>
                    <div className="mt-3">
                      {validHours.length > 0 ? (
                        <div className="space-y-1">
                          {validHours.slice(0, 2).map((timeSlot, i) => (
                            <div key={i} className="flex items-center justify-center">
                              <ClockIcon className={`h-3 w-3 mr-1 ${timeSlot.isRecurring ? 'text-blue-400' : 'text-gray-400'}`} />
                              <span className={`text-xs ${timeSlot.isRecurring ? 'text-blue-600' : 'text-gray-600'}`}>
                                {timeSlot.start.slice(0, 5)}-{timeSlot.end.slice(0, 5)}
                                {!timeSlot.isRecurring && <span className="ml-0.5">*</span>}
                              </span>
                            </div>
                          ))}
                          {validHours.length > 2 && (
                            <div className="text-xs text-center text-gray-500">
                              +{validHours.length - 2} daha
                            </div>
                          )}
                          {oneTimeHours.length > 0 && recurringHours.length > 0 && (
                            <div className="text-xs text-center text-gray-400 mt-1">
                              * Tek seferlik
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-center">
                          <div className="w-8 h-8 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-2">
                            <span className="text-gray-400 text-sm">×</span>
                          </div>
                          <span className="text-xs text-gray-400">Müsait değil</span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Seçilen Günün Müsaitlik Detayları */}
        {selectedDay && (
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="flex justify-between items-center border-b border-gray-200 pb-3 mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {format(selectedDay.date, 'EEEE, d MMMM', { locale: tr })} için Çalışma Saatleri
                </h3>
                <button
                  onClick={handleAddTimeSlot}
                  disabled={isEditing}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Saat Ekle
                </button>
              </div>

              {isEditing ? (
                <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="startTime" className="block text-sm font-medium text-gray-700">
                        Başlangıç Saati
                      </label>
                      <select
                        id="startTime"
                        value={startTime}
                        onChange={(e) => setStartTime(e.target.value)}
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                      >
                        {timeOptions.map((time) => (
                          <option key={time} value={time}>
                            {time}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label htmlFor="endTime" className="block text-sm font-medium text-gray-700">
                        Bitiş Saati
                      </label>
                      <select
                        id="endTime"
                        value={endTime}
                        onChange={(e) => setEndTime(e.target.value)}
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                      >
                        {timeOptions.map((time) => (
                          <option key={time} value={time}>
                            {time}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="mt-4">
                    <fieldset>
                      <legend className="block text-sm font-medium text-gray-700">Tekrarlanma</legend>
                      <div className="mt-2">
                        <div className="relative flex items-start">
                          <div className="flex h-5 items-center">
                            <input
                              id="recurring-weekly"
                              name="recurring-type"
                              type="checkbox"
                              checked={isRecurring}
                              onChange={() => setIsRecurring(!isRecurring)}
                              className="h-5 w-5 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            />
                          </div>
                          <div className="ml-3 text-sm">
                            <label htmlFor="recurring-weekly" className="font-medium text-gray-700">
                              Her hafta tekrarla
                            </label>
                            <p className="text-gray-500">
                              {isRecurring 
                                ? 'Bu saatler her hafta aynı günde geçerli olacak.' 
                                : `Bu saatler sadece ${format(selectedDay.date, 'd MMMM', { locale: tr })} tarihinde geçerli olacak.`}
                            </p>
                          </div>
                        </div>
                      </div>
                    </fieldset>
                  </div>

                  <div className="mt-4 flex justify-end space-x-2">
                    <button
                      onClick={handleCancelEdit}
                      className="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <XMarkIcon className="h-4 w-4 mr-1.5" />
                      İptal
                    </button>
                    <button
                      onClick={handleSaveTimeSlot}
                      disabled={isSaving}
                      className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                    >
                      {isSaving ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Kaydediliyor...
                        </>
                      ) : (
                        <>
                          <CheckIcon className="h-4 w-4 mr-1.5" />
                          Kaydet
                        </>
                      )}
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  {selectedDay.hours.length === 0 ? (
                    <div className="text-center py-6 text-gray-500">
                      <CalendarIcon className="h-12 w-12 mx-auto text-gray-400" />
                      <p className="mt-2 text-sm">Bu gün için henüz çalışma saati belirlenmemiş.</p>
                      <p className="text-xs text-gray-400">Yeni çalışma saati eklemek için "Saat Ekle" butonuna tıklayın.</p>
                    </div>
                  ) : (
                    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                      <table className="min-w-full divide-y divide-gray-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th 
                              scope="col" 
                              className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              Başlangıç Saati
                            </th>
                            <th 
                              scope="col" 
                              className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              Bitiş Saati
                            </th>
                            <th 
                              scope="col" 
                              className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                            >
                              Tekrarlama
                            </th>
                            <th 
                              scope="col" 
                              className="relative py-3.5 pl-3 pr-4 sm:pr-6 text-right"
                            >
                              <span className="sr-only">İşlemler</span>
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white">
                          {selectedDay.hours.map((timeSlot) => (
                            <tr key={timeSlot.id}>
                              <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {timeSlot.start}
                              </td>
                              <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {timeSlot.end}
                              </td>
                              <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                {timeSlot.isRecurring ? (
                                  <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                                    Her hafta
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                                    Tek seferlik
                                  </span>
                                )}
                              </td>
                              <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                <div className="flex space-x-2 justify-end">
                                  <button
                                    onClick={() => handleEditTimeSlot(timeSlot)}
                                    className="text-primary-600 hover:text-primary-900"
                                    disabled={isEditing}
                                  >
                                    <PencilIcon className="h-4 w-4" />
                                    <span className="sr-only">Düzenle</span>
                                  </button>
                                  <button
                                    onClick={() => handleDeleteTimeSlot(timeSlot.id)}
                                    className="text-red-600 hover:text-red-900"
                                    disabled={isEditing || isSaving}
                                  >
                                    <TrashIcon className="h-4 w-4" />
                                    <span className="sr-only">Sil</span>
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AvailabilityPage; 